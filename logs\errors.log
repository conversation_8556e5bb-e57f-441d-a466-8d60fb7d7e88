2025-07-12 22:11:42 | ERROR    | src.ai_manager:process_invoice:74 - Error with provider deepseek: DeepSeek API error: 422 - Failed to deserialize the JSON body into the target type: messages[1]: data did not match any variant of untagged enum ChatCompletionRequestContent at line 1 column 2956944
2025-07-12 22:11:42 | ERROR    | src.invoice_processor:process_single_invoice:192 - Failed to process G:\vscodeapps\augment\invoicerecorder\data\invoices\Invoice_ (3).jpg: All providers failed. Last error: DeepSeek API error: 422 - Failed to deserialize the JSON body into the target type: messages[1]: data did not match any variant of untagged enum ChatCompletionRequestContent at line 1 column 2956944
2025-07-12 22:11:42 | ERROR    | src.ai_manager:process_invoice:74 - Error with provider deepseek: DeepSeek API error: 422 - Failed to deserialize the JSON body into the target type: messages[1]: data did not match any variant of untagged enum ChatCompletionRequestContent at line 1 column 3181756
2025-07-12 22:11:42 | ERROR    | src.invoice_processor:process_single_invoice:192 - Failed to process G:\vscodeapps\augment\invoicerecorder\data\invoices\Invoice_ (2).jpg: All providers failed. Last error: DeepSeek API error: 422 - Failed to deserialize the JSON body into the target type: messages[1]: data did not match any variant of untagged enum ChatCompletionRequestContent at line 1 column 3181756
2025-07-12 22:11:45 | ERROR    | src.ai_manager:process_invoice:74 - Error with provider deepseek: DeepSeek API error: 422 - Failed to deserialize the JSON body into the target type: messages[1]: data did not match any variant of untagged enum ChatCompletionRequestContent at line 1 column 3942368
2025-07-12 22:11:45 | ERROR    | src.invoice_processor:process_single_invoice:192 - Failed to process G:\vscodeapps\augment\invoicerecorder\data\invoices\Invoice_ (1).jpg: All providers failed. Last error: DeepSeek API error: 422 - Failed to deserialize the JSON body into the target type: messages[1]: data did not match any variant of untagged enum ChatCompletionRequestContent at line 1 column 3942368
2025-07-12 22:11:57 | ERROR    | src.ai_manager:process_invoice:74 - Error with provider deepseek: DeepSeek API error: 422 - Failed to deserialize the JSON body into the target type: messages[1]: data did not match any variant of untagged enum ChatCompletionRequestContent at line 1 column 4593648
2025-07-12 22:11:57 | ERROR    | src.invoice_processor:process_single_invoice:192 - Failed to process G:\vscodeapps\augment\invoicerecorder\data\invoices\Invoice_ (4).jpg: All providers failed. Last error: DeepSeek API error: 422 - Failed to deserialize the JSON body into the target type: messages[1]: data did not match any variant of untagged enum ChatCompletionRequestContent at line 1 column 4593648

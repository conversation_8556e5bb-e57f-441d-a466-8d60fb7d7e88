#!/usr/bin/env python3
"""
Invoice Processor - Main CLI Application
"""

import asyncio
import sys
from pathlib import Path
from typing import Optional, List
import typer
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn, TaskProgressColumn
from rich.prompt import Prompt, Confirm
from rich.text import Text
from rich import print as rprint
from loguru import logger

# Add src to path for imports
sys.path.append(str(Path(__file__).parent))

from config.settings import settings
from src.invoice_processor import InvoiceProcessor
from src.excel_manager import ExcelManager
from src.ai_manager import AIManager
from src.logging_config import logging_manager, ProgressTracker, HealthChecker

# Initialize CLI app
app = typer.Typer(
    name="invoice-processor",
    help="AI-powered invoice processing system with multi-provider support",
    rich_markup_mode="rich"
)
console = Console()

# Global instances
invoice_processor = None
excel_manager = None
ai_manager = None

def initialize_components():
    """Initialize application components."""
    global invoice_processor, excel_manager, ai_manager
    
    try:
        invoice_processor = InvoiceProcessor()
        excel_manager = ExcelManager()
        ai_manager = AIManager()
        return True
    except Exception as e:
        console.print(f"[red]Error initializing components: {str(e)}[/red]")
        return False

@app.command()
def process(
    input_folder: Optional[str] = typer.Option(
        None, 
        "--input-folder", 
        "-i", 
        help="Input folder containing invoice files"
    ),
    provider: Optional[str] = typer.Option(
        None, 
        "--provider", 
        "-p", 
        help="Specific AI provider to use"
    ),
    output_file: Optional[str] = typer.Option(
        None, 
        "--output", 
        "-o", 
        help="Output Excel filename"
    ),
    batch_size: int = typer.Option(
        5, 
        "--batch-size", 
        "-b", 
        help="Number of files to process concurrently"
    ),
    preprocess: bool = typer.Option(
        True, 
        "--preprocess/--no-preprocess", 
        help="Enable/disable image preprocessing"
    )
):
    """Process invoice files using AI providers."""
    
    if not initialize_components():
        raise typer.Exit(1)
    
    # Determine input folder
    if input_folder:
        input_path = Path(input_folder)
    else:
        input_path = settings.invoices_dir
    
    if not input_path.exists():
        console.print(f"[red]Input folder does not exist: {input_path}[/red]")
        raise typer.Exit(1)
    
    # Display processing configuration
    config_panel = Panel(
        f"""[bold]Processing Configuration[/bold]
        
Input Folder: [cyan]{input_path}[/cyan]
AI Provider: [green]{provider or settings.default_ai_provider}[/green]
Batch Size: [yellow]{batch_size}[/yellow]
Preprocessing: [blue]{'Enabled' if preprocess else 'Disabled'}[/blue]
Output File: [magenta]{output_file or 'Auto-generated'}[/magenta]""",
        title="Configuration",
        border_style="blue"
    )
    console.print(config_panel)
    
    # Discover files
    with console.status("[bold green]Discovering invoice files..."):
        discovered_files = invoice_processor.discover_invoice_files(input_path)
    
    if not discovered_files:
        console.print("[yellow]No invoice files found in the specified directory.[/yellow]")
        raise typer.Exit(0)
    
    console.print(f"[green]Found {len(discovered_files)} invoice files[/green]")
    
    # Confirm processing
    if not Confirm.ask(f"Process {len(discovered_files)} files?"):
        console.print("[yellow]Processing cancelled.[/yellow]")
        raise typer.Exit(0)
    
    # Process files
    asyncio.run(process_files_async(
        discovered_files, 
        provider, 
        output_file, 
        batch_size, 
        preprocess
    ))

async def process_files_async(
    files: List[Path], 
    provider: Optional[str], 
    output_file: Optional[str], 
    batch_size: int, 
    preprocess: bool
):
    """Async processing of files with progress tracking."""
    
    results = []
    
    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        BarColumn(),
        TaskProgressColumn(),
        console=console
    ) as progress:
        
        task = progress.add_task("Processing invoices...", total=len(files))
        
        # Process in batches
        for i in range(0, len(files), batch_size):
            batch = files[i:i + batch_size]
            
            try:
                batch_results = await invoice_processor.process_batch(
                    batch, 
                    provider, 
                    max_concurrent=batch_size
                )
                results.extend(batch_results)
                
                # Update progress
                progress.update(task, advance=len(batch))
                
                # Show batch results
                successful = sum(1 for r in batch_results if r.success)
                console.print(f"[green]Batch completed: {successful}/{len(batch)} successful[/green]")
                
            except Exception as e:
                console.print(f"[red]Batch processing error: {str(e)}[/red]")
                progress.update(task, advance=len(batch))
    
    # Display results summary
    display_processing_results(results)
    
    # Save to Excel if we have successful results
    successful_results = [r for r in results if r.success]
    if successful_results:
        save_results_to_excel(successful_results, output_file)
    else:
        console.print("[red]No successful results to save.[/red]")

def display_processing_results(results: List):
    """Display processing results in a formatted table."""
    
    # Create results table
    table = Table(title="Processing Results")
    table.add_column("File", style="cyan", no_wrap=True)
    table.add_column("Status", style="bold")
    table.add_column("Provider", style="green")
    table.add_column("Time (s)", justify="right", style="yellow")
    table.add_column("Confidence", justify="right", style="blue")
    table.add_column("Error", style="red")
    
    successful = 0
    failed = 0
    total_time = 0.0
    
    for result in results:
        if result.success:
            successful += 1
            status = "[green]✓ Success[/green]"
            confidence = f"{result.data.get('confidence', 0.0):.2%}"
            error = ""
        else:
            failed += 1
            status = "[red]✗ Failed[/red]"
            confidence = ""
            error = result.error[:50] + "..." if result.error and len(result.error) > 50 else result.error or ""
        
        total_time += result.processing_time
        
        table.add_row(
            result.file_path.name,
            status,
            result.provider_used or "N/A",
            f"{result.processing_time:.2f}",
            confidence,
            error
        )
    
    console.print(table)
    
    # Summary panel
    summary = Panel(
        f"""[bold]Processing Summary[/bold]

Total Files: [cyan]{len(results)}[/cyan]
Successful: [green]{successful}[/green]
Failed: [red]{failed}[/red]
Success Rate: [yellow]{(successful/len(results)*100):.1f}%[/yellow]
Total Time: [blue]{total_time:.1f}s[/blue]
Average Time: [magenta]{(total_time/len(results)):.2f}s per file[/magenta]""",
        title="Summary",
        border_style="green"
    )
    console.print(summary)

def save_results_to_excel(results: List, output_file: Optional[str]):
    """Save processing results to Excel."""
    
    with console.status("[bold green]Saving results to Excel..."):
        try:
            # Extract data from results
            invoice_data = [result.data for result in results if result.success]
            
            # Save to Excel
            excel_path = excel_manager.save_invoice_data(invoice_data, output_file)
            
            console.print(f"[green]✓ Results saved to: {excel_path}[/green]")
            
            # Display Excel statistics
            stats = excel_manager.get_invoice_statistics(excel_path)
            if stats:
                stats_table = Table(title="Excel File Statistics")
                stats_table.add_column("Metric", style="cyan")
                stats_table.add_column("Value", style="green")
                
                stats_table.add_row("Total Sheets", str(stats.get('total_sheets', 0)))
                stats_table.add_row("Total Invoices", str(stats.get('total_invoices', 0)))
                stats_table.add_row("Total Amount", f"${stats.get('total_amount', 0.0):,.2f}")
                
                console.print(stats_table)
            
        except Exception as e:
            console.print(f"[red]Error saving to Excel: {str(e)}[/red]")

@app.command()
def configure():
    """Interactive configuration setup."""
    
    console.print(Panel(
        "[bold]Invoice Processor Configuration[/bold]\n\n"
        "This wizard will help you configure AI providers and application settings.",
        title="Configuration Wizard",
        border_style="blue"
    ))
    
    # AI Provider Configuration
    console.print("\n[bold cyan]AI Provider Configuration[/bold cyan]")
    
    providers = ["deepseek", "openai", "anthropic", "google", "openrouter", "mistral"]
    
    for provider in providers:
        current_key = settings.get_api_key(provider)
        status = "[green]✓ Configured[/green]" if current_key else "[red]✗ Not configured[/red]"
        
        console.print(f"\n{provider.title()}: {status}")
        
        if not current_key or Confirm.ask(f"Update {provider} API key?"):
            api_key = Prompt.ask(f"Enter {provider} API key", password=True)
            if api_key:
                # Here you would save to .env file
                console.print(f"[green]✓ {provider} API key updated[/green]")
    
    # Test connections
    if Confirm.ask("\nTest AI provider connections?"):
        test_connections()

@app.command()
def test_connections():
    """Test AI provider connections."""
    
    if not initialize_components():
        raise typer.Exit(1)
    
    console.print("[bold]Testing AI Provider Connections[/bold]\n")
    
    # Create test table
    table = Table(title="Provider Connection Tests")
    table.add_column("Provider", style="cyan")
    table.add_column("Status", style="bold")
    table.add_column("Model", style="green")
    table.add_column("Message", style="yellow")
    
    async def run_tests():
        for provider in ai_manager.available_providers:
            with console.status(f"Testing {provider}..."):
                try:
                    result = await ai_manager.test_provider(provider)
                    
                    if result.get("status") == "success":
                        status = "[green]✓ Connected[/green]"
                    else:
                        status = "[red]✗ Failed[/red]"
                    
                    config = ai_manager.get_provider_status().get(provider, {})
                    model = config.get("model", "Unknown")
                    message = result.get("message", "")
                    
                    table.add_row(provider.title(), status, model, message)
                    
                except Exception as e:
                    table.add_row(
                        provider.title(), 
                        "[red]✗ Error[/red]", 
                        "Unknown", 
                        str(e)[:50]
                    )
    
    asyncio.run(run_tests())
    console.print(table)

@app.command()
def stats(
    excel_file: Optional[str] = typer.Option(
        None, 
        "--file", 
        "-f", 
        help="Specific Excel file to analyze"
    )
):
    """Display processing statistics."""
    
    if not initialize_components():
        raise typer.Exit(1)
    
    # Processing statistics
    processing_stats = invoice_processor.get_processing_stats()
    
    stats_table = Table(title="Processing Statistics")
    stats_table.add_column("Metric", style="cyan")
    stats_table.add_column("Value", style="green")
    
    stats_table.add_row("Total Processed", str(processing_stats.get('total_processed', 0)))
    stats_table.add_row("Successful", str(processing_stats.get('successful', 0)))
    stats_table.add_row("Failed", str(processing_stats.get('failed', 0)))
    stats_table.add_row("Success Rate", f"{processing_stats.get('success_rate', 0.0):.1%}")
    stats_table.add_row("Average Time", f"{processing_stats.get('average_processing_time', 0.0):.2f}s")
    
    console.print(stats_table)
    
    # Excel file statistics
    if excel_file:
        excel_path = Path(excel_file)
    else:
        # Find most recent Excel file
        excel_files = list(settings.output_dir.glob("*.xlsx"))
        if excel_files:
            excel_path = max(excel_files, key=lambda p: p.stat().st_mtime)
        else:
            console.print("[yellow]No Excel files found.[/yellow]")
            return
    
    if excel_path.exists():
        excel_stats = excel_manager.get_invoice_statistics(excel_path)
        
        if excel_stats:
            excel_table = Table(title=f"Excel Statistics: {excel_path.name}")
            excel_table.add_column("Month", style="cyan")
            excel_table.add_column("Invoices", justify="right", style="green")
            excel_table.add_column("Total Amount", justify="right", style="yellow")
            
            for month, data in excel_stats.get('monthly_breakdown', {}).items():
                excel_table.add_row(
                    month,
                    str(data['invoices']),
                    f"${data['total']:,.2f}"
                )
            
            console.print(excel_table)

@app.command()
def health():
    """Check system health and requirements."""
    
    console.print("[bold]System Health Check[/bold]\n")
    
    # Check disk space
    disk_status = HealthChecker.check_disk_space(settings.base_dir)
    
    # Check memory
    memory_status = HealthChecker.check_memory_usage()
    
    # Create health table
    health_table = Table(title="System Health")
    health_table.add_column("Component", style="cyan")
    health_table.add_column("Status", style="bold")
    health_table.add_column("Details", style="yellow")
    
    # Disk space
    if disk_status.get("sufficient_space"):
        disk_status_text = "[green]✓ OK[/green]"
    else:
        disk_status_text = "[red]✗ Low Space[/red]"
    
    health_table.add_row(
        "Disk Space",
        disk_status_text,
        f"{disk_status.get('free_gb', 0):.1f}GB free"
    )
    
    # Memory
    if memory_status.get("sufficient_memory"):
        memory_status_text = "[green]✓ OK[/green]"
    else:
        memory_status_text = "[red]✗ High Usage[/red]"
    
    health_table.add_row(
        "Memory",
        memory_status_text,
        f"{memory_status.get('usage_percent', 0):.1f}% used"
    )
    
    # Directories
    try:
        settings.ensure_directories()
        dir_status = "[green]✓ OK[/green]"
        dir_details = "All directories accessible"
    except Exception as e:
        dir_status = "[red]✗ Error[/red]"
        dir_details = str(e)[:50]
    
    health_table.add_row("Directories", dir_status, dir_details)
    
    console.print(health_table)

@app.command()
def version():
    """Display version information."""
    
    version_info = Panel(
        """[bold]Invoice Processor v1.0.0[/bold]

AI-powered invoice processing system with multi-provider support.

Features:
• Multiple AI providers (DeepSeek, OpenAI, Claude, Gemini, etc.)
• Automatic Excel organization by month/year
• Image preprocessing and enhancement
• Batch processing with progress tracking
• Comprehensive error handling and logging
• Rich CLI interface

Developed with ❤️ using Python, Typer, and Rich.""",
        title="Version Information",
        border_style="blue"
    )
    
    console.print(version_info)

if __name__ == "__main__":
    app()

"""
Excel Management System for Invoice Data.
"""

import pandas as pd
from datetime import datetime, date
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
import openpyxl
from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
from openpyxl.utils.dataframe import dataframe_to_rows
from openpyxl.formatting.rule import ColorScaleRule
from loguru import logger

from config.settings import settings
from src.utils import DateProcessor, CurrencyProcessor

class ExcelManagerError(Exception):
    """Custom exception for Excel management errors."""
    pass

class ExcelManager:
    """Manages Excel file operations for invoice data."""
    
    def __init__(self):
        self.output_dir = settings.output_dir
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # Excel styling
        self.header_font = Font(bold=True, color="FFFFFF")
        self.header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
        self.header_alignment = Alignment(horizontal="center", vertical="center")
        self.border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )
        
        logger.info("Excel Manager initialized")
    
    def save_invoice_data(
        self, 
        invoice_data: List[Dict[str, Any]], 
        filename: Optional[str] = None
    ) -> Path:
        """
        Save invoice data to Excel file organized by month/year.
        
        Args:
            invoice_data: List of invoice data dictionaries
            filename: Custom filename (optional)
            
        Returns:
            Path to saved Excel file
        """
        if not invoice_data:
            raise ExcelManagerError("No invoice data provided")
        
        # Group data by year and month
        grouped_data = self._group_by_month_year(invoice_data)
        
        # Determine filename
        if filename:
            excel_path = self.output_dir / filename
        else:
            current_year = datetime.now().year
            excel_path = self.output_dir / f"Invoices_{current_year}.xlsx"
        
        # Create or update Excel file
        if excel_path.exists():
            self._update_existing_excel(excel_path, grouped_data)
        else:
            self._create_new_excel(excel_path, grouped_data)
        
        logger.success(f"Invoice data saved to: {excel_path}")
        return excel_path
    
    def _group_by_month_year(self, invoice_data: List[Dict[str, Any]]) -> Dict[str, List[Dict[str, Any]]]:
        """Group invoice data by month and year."""
        grouped = {}
        
        for invoice in invoice_data:
            # Parse date
            date_str = invoice.get('date')
            if not date_str:
                # Use current date if no date found
                invoice_date = datetime.now()
            else:
                try:
                    invoice_date = datetime.strptime(date_str, "%d/%m/%Y")
                except ValueError:
                    # Try to parse with dateutil
                    try:
                        from dateutil import parser
                        invoice_date = parser.parse(date_str)
                    except:
                        invoice_date = datetime.now()
            
            # Create month-year key
            month_year = f"{invoice_date.year}-{invoice_date.month:02d}"
            
            if month_year not in grouped:
                grouped[month_year] = []
            
            grouped[month_year].append(invoice)
        
        return grouped
    
    def _create_new_excel(self, excel_path: Path, grouped_data: Dict[str, List[Dict[str, Any]]]):
        """Create a new Excel file with invoice data."""
        workbook = openpyxl.Workbook()
        
        # Remove default sheet
        workbook.remove(workbook.active)
        
        # Create sheets for each month
        for month_year, invoices in grouped_data.items():
            year, month = month_year.split('-')
            month_name = datetime(int(year), int(month), 1).strftime("%B %Y")
            
            # Create worksheet
            worksheet = workbook.create_sheet(title=month_name)
            
            # Convert to DataFrame and add to sheet
            df = self._create_dataframe(invoices)
            self._write_dataframe_to_sheet(worksheet, df, month_name)
        
        # Create summary sheet
        self._create_summary_sheet(workbook, grouped_data)
        
        # Save workbook
        workbook.save(excel_path)
    
    def _update_existing_excel(self, excel_path: Path, grouped_data: Dict[str, List[Dict[str, Any]]]):
        """Update existing Excel file with new invoice data."""
        try:
            workbook = openpyxl.load_workbook(excel_path)
        except Exception as e:
            logger.warning(f"Could not load existing Excel file, creating new one: {str(e)}")
            self._create_new_excel(excel_path, grouped_data)
            return
        
        for month_year, invoices in grouped_data.items():
            year, month = month_year.split('-')
            month_name = datetime(int(year), int(month), 1).strftime("%B %Y")
            
            # Check if sheet exists
            if month_name in workbook.sheetnames:
                # Update existing sheet
                worksheet = workbook[month_name]
                existing_df = self._read_sheet_to_dataframe(worksheet)
                new_df = self._create_dataframe(invoices)
                
                # Combine and remove duplicates
                combined_df = pd.concat([existing_df, new_df], ignore_index=True)
                combined_df = self._remove_duplicate_invoices(combined_df)
                
                # Clear sheet and rewrite
                worksheet.delete_rows(1, worksheet.max_row)
                self._write_dataframe_to_sheet(worksheet, combined_df, month_name)
            else:
                # Create new sheet
                worksheet = workbook.create_sheet(title=month_name)
                df = self._create_dataframe(invoices)
                self._write_dataframe_to_sheet(worksheet, df, month_name)
        
        # Update summary sheet
        if "Summary" in workbook.sheetnames:
            workbook.remove(workbook["Summary"])
        
        self._create_summary_sheet(workbook, grouped_data)
        
        # Save workbook
        workbook.save(excel_path)
    
    def _create_dataframe(self, invoices: List[Dict[str, Any]]) -> pd.DataFrame:
        """Create pandas DataFrame from invoice data."""
        rows = []
        
        for invoice in invoices:
            # Handle products
            products = invoice.get('products', [])
            if products:
                for product in products:
                    row = {
                        'Date': invoice.get('date', ''),
                        'Vendor Name': invoice.get('vendor_name', ''),
                        'Product Details': product.get('description', ''),
                        'Quantity': product.get('quantity', 1),
                        'Unit Price': product.get('unit_price', 0.0),
                        'Total Amount': product.get('total_price', 0.0),
                        'Currency': invoice.get('currency', 'USD'),
                        'Invoice Number': invoice.get('invoice_number', ''),
                        'Tax Amount': invoice.get('tax_amount', 0.0),
                        'Payment Method': invoice.get('payment_method', ''),
                        'Confidence': invoice.get('confidence', 0.0),
                        'File Path': invoice.get('file_path', ''),
                        'Processed At': invoice.get('processed_at', '')
                    }
                    rows.append(row)
            else:
                # No products, create single row
                row = {
                    'Date': invoice.get('date', ''),
                    'Vendor Name': invoice.get('vendor_name', ''),
                    'Product Details': 'N/A',
                    'Quantity': 1,
                    'Unit Price': invoice.get('total_amount', 0.0),
                    'Total Amount': invoice.get('total_amount', 0.0),
                    'Currency': invoice.get('currency', 'USD'),
                    'Invoice Number': invoice.get('invoice_number', ''),
                    'Tax Amount': invoice.get('tax_amount', 0.0),
                    'Payment Method': invoice.get('payment_method', ''),
                    'Confidence': invoice.get('confidence', 0.0),
                    'File Path': invoice.get('file_path', ''),
                    'Processed At': invoice.get('processed_at', '')
                }
                rows.append(row)
        
        df = pd.DataFrame(rows)
        
        # Sort by date
        if not df.empty and 'Date' in df.columns:
            df['Date'] = pd.to_datetime(df['Date'], format='%d/%m/%Y', errors='coerce')
            df = df.sort_values('Date')
            df['Date'] = df['Date'].dt.strftime('%d/%m/%Y')
        
        return df
    
    def _write_dataframe_to_sheet(self, worksheet, df: pd.DataFrame, sheet_name: str):
        """Write DataFrame to Excel worksheet with formatting."""
        if df.empty:
            return
        
        # Write data
        for r in dataframe_to_rows(df, index=False, header=True):
            worksheet.append(r)
        
        # Apply formatting
        self._format_worksheet(worksheet, df)
        
        # Set column widths
        self._adjust_column_widths(worksheet)
    
    def _format_worksheet(self, worksheet, df: pd.DataFrame):
        """Apply formatting to worksheet."""
        # Header formatting
        for cell in worksheet[1]:
            cell.font = self.header_font
            cell.fill = self.header_fill
            cell.alignment = self.header_alignment
            cell.border = self.border
        
        # Data formatting
        for row in worksheet.iter_rows(min_row=2, max_row=worksheet.max_row):
            for cell in row:
                cell.border = self.border
                
                # Format currency columns
                if cell.column_letter in ['E', 'F', 'I']:  # Unit Price, Total Amount, Tax Amount
                    cell.number_format = '#,##0.00'
                
                # Format date column
                if cell.column_letter == 'A':  # Date
                    cell.number_format = 'DD/MM/YYYY'
                
                # Format confidence column
                if cell.column_letter == 'K':  # Confidence
                    cell.number_format = '0.00%'
        
        # Add conditional formatting for confidence scores
        if worksheet.max_row > 1:
            confidence_range = f"K2:K{worksheet.max_row}"
            rule = ColorScaleRule(
                start_type='num', start_value=0, start_color='FF6B6B',
                mid_type='num', mid_value=0.5, mid_color='FFE66D',
                end_type='num', end_value=1, end_color='4ECDC4'
            )
            worksheet.conditional_formatting.add(confidence_range, rule)
    
    def _adjust_column_widths(self, worksheet):
        """Adjust column widths based on content."""
        column_widths = {
            'A': 12,  # Date
            'B': 20,  # Vendor Name
            'C': 30,  # Product Details
            'D': 10,  # Quantity
            'E': 12,  # Unit Price
            'F': 12,  # Total Amount
            'G': 10,  # Currency
            'H': 15,  # Invoice Number
            'I': 12,  # Tax Amount
            'J': 15,  # Payment Method
            'K': 12,  # Confidence
            'L': 25,  # File Path
            'M': 20,  # Processed At
        }
        
        for column, width in column_widths.items():
            worksheet.column_dimensions[column].width = width
    
    def _create_summary_sheet(self, workbook, grouped_data: Dict[str, List[Dict[str, Any]]]):
        """Create summary sheet with statistics."""
        summary_sheet = workbook.create_sheet(title="Summary", index=0)
        
        # Summary data
        summary_data = []
        total_amount = 0.0
        total_invoices = 0
        
        for month_year, invoices in grouped_data.items():
            year, month = month_year.split('-')
            month_name = datetime(int(year), int(month), 1).strftime("%B %Y")
            
            month_total = sum(invoice.get('total_amount', 0.0) for invoice in invoices)
            invoice_count = len(invoices)
            
            summary_data.append({
                'Month': month_name,
                'Invoice Count': invoice_count,
                'Total Amount': month_total,
                'Average Amount': month_total / invoice_count if invoice_count > 0 else 0.0
            })
            
            total_amount += month_total
            total_invoices += invoice_count
        
        # Write summary data
        summary_df = pd.DataFrame(summary_data)
        
        # Add headers
        summary_sheet.append(['Monthly Invoice Summary'])
        summary_sheet.append([])  # Empty row
        
        # Write DataFrame
        for r in dataframe_to_rows(summary_df, index=False, header=True):
            summary_sheet.append(r)
        
        # Add totals
        summary_sheet.append([])
        summary_sheet.append(['Total', total_invoices, total_amount, total_amount / total_invoices if total_invoices > 0 else 0.0])
        
        # Format summary sheet
        self._format_summary_sheet(summary_sheet)
    
    def _format_summary_sheet(self, worksheet):
        """Format the summary sheet."""
        # Title formatting
        worksheet['A1'].font = Font(bold=True, size=14)
        worksheet.merge_cells('A1:D1')
        
        # Header formatting (row 3)
        for cell in worksheet[3]:
            if cell.value:
                cell.font = self.header_font
                cell.fill = self.header_fill
                cell.alignment = self.header_alignment
                cell.border = self.border
        
        # Data formatting
        for row in worksheet.iter_rows(min_row=4, max_row=worksheet.max_row):
            for cell in row:
                if cell.value is not None:
                    cell.border = self.border
                    
                    # Format currency columns
                    if cell.column_letter in ['C', 'D']:  # Total Amount, Average Amount
                        cell.number_format = '#,##0.00'
        
        # Adjust column widths
        worksheet.column_dimensions['A'].width = 15
        worksheet.column_dimensions['B'].width = 15
        worksheet.column_dimensions['C'].width = 15
        worksheet.column_dimensions['D'].width = 15
    
    def _read_sheet_to_dataframe(self, worksheet) -> pd.DataFrame:
        """Read Excel sheet to pandas DataFrame."""
        data = []
        headers = []
        
        # Get headers from first row
        for cell in worksheet[1]:
            headers.append(cell.value)
        
        # Get data from remaining rows
        for row in worksheet.iter_rows(min_row=2, values_only=True):
            if any(cell is not None for cell in row):
                data.append(row)
        
        if data:
            df = pd.DataFrame(data, columns=headers)
            return df
        else:
            return pd.DataFrame()
    
    def _remove_duplicate_invoices(self, df: pd.DataFrame) -> pd.DataFrame:
        """Remove duplicate invoices based on key fields."""
        if df.empty:
            return df
        
        # Define columns to check for duplicates
        duplicate_columns = ['Date', 'Vendor Name', 'Total Amount', 'Invoice Number']
        
        # Remove duplicates, keeping the first occurrence
        df_cleaned = df.drop_duplicates(subset=duplicate_columns, keep='first')
        
        duplicates_removed = len(df) - len(df_cleaned)
        if duplicates_removed > 0:
            logger.info(f"Removed {duplicates_removed} duplicate entries")
        
        return df_cleaned
    
    def export_to_csv(self, excel_path: Path, output_dir: Optional[Path] = None) -> List[Path]:
        """Export Excel sheets to CSV files."""
        if output_dir is None:
            output_dir = excel_path.parent / "csv_exports"
        
        output_dir.mkdir(parents=True, exist_ok=True)
        
        try:
            workbook = openpyxl.load_workbook(excel_path)
            csv_files = []
            
            for sheet_name in workbook.sheetnames:
                if sheet_name == "Summary":
                    continue
                
                worksheet = workbook[sheet_name]
                df = self._read_sheet_to_dataframe(worksheet)
                
                if not df.empty:
                    csv_path = output_dir / f"{sheet_name.replace(' ', '_')}.csv"
                    df.to_csv(csv_path, index=False)
                    csv_files.append(csv_path)
            
            logger.info(f"Exported {len(csv_files)} CSV files to {output_dir}")
            return csv_files
            
        except Exception as e:
            logger.error(f"Error exporting to CSV: {str(e)}")
            return []
    
    def get_invoice_statistics(self, excel_path: Path) -> Dict[str, Any]:
        """Get statistics from Excel file."""
        try:
            workbook = openpyxl.load_workbook(excel_path)
            stats = {
                'total_sheets': len(workbook.sheetnames) - 1,  # Exclude summary
                'total_invoices': 0,
                'total_amount': 0.0,
                'monthly_breakdown': {}
            }
            
            for sheet_name in workbook.sheetnames:
                if sheet_name == "Summary":
                    continue
                
                worksheet = workbook[sheet_name]
                df = self._read_sheet_to_dataframe(worksheet)
                
                if not df.empty:
                    month_invoices = len(df)
                    month_total = df['Total Amount'].sum() if 'Total Amount' in df.columns else 0.0
                    
                    stats['total_invoices'] += month_invoices
                    stats['total_amount'] += month_total
                    stats['monthly_breakdown'][sheet_name] = {
                        'invoices': month_invoices,
                        'total': month_total
                    }
            
            return stats
            
        except Exception as e:
            logger.error(f"Error getting statistics: {str(e)}")
            return {}
